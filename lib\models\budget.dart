import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'budget.g.dart';

@HiveType(typeId: 4)
class Budget extends HiveObject {
  @HiveField(0)
  late String localUuid;

  @HiveField(1)
  late DateTime period;

  @HiveField(2)
  late double amount;

  @HiveField(3)
  late String categoryUuid; // Foreign key to Category's localUuid

  @HiveField(4)
  late String userId;

  @HiveField(5)
  int? supabaseId;

  @HiveField(6)
  late DateTime lastModified;

  @HiveField(7)
  bool needsSync;

  @HiveField(8)
  bool isDeleted;

  Budget({
    String? localUuid,
    required this.period,
    required this.amount,
    required this.categoryUuid,
    required this.userId,
    this.supabaseId,
    DateTime? lastModified,
    this.needsSync = true,
    this.isDeleted = false,
  }) {
    this.localUuid = localUuid ?? const Uuid().v4();
    this.lastModified = lastModified ?? DateTime.now().toUtc();
  }

  Map<String, dynamic> toJson({int? categorySupabaseId}) {
    return {
      'local_uuid': localUuid,
      'period': period.toIso8601String(),
      'amount': amount,
      'category_id': categorySupabaseId,
      'user_id': userId,
      'is_deleted': isDeleted,
      'last_modified': lastModified.toIso8601String(),
    };
  }
  
  static Budget? fromJson(Map<String, dynamic> json) {
    // This will be handled in the SyncService
    return null;
  }
}