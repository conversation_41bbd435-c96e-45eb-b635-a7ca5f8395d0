import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'category.g.dart';

@HiveType(typeId: 2)
class Category extends HiveObject {
  // Local primary key
  @HiveField(0)
  late String localUuid;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String type;

  @HiveField(3)
  late String userId;

  // Supabase primary key (auto-incrementing integer)
  @HiveField(4)
  int? supabaseId;

  @HiveField(5)
  late DateTime lastModified;

  @HiveField(6)
  bool needsSync;

  @HiveField(7)
  bool isDeleted;

  Category({
    String? localUuid,
    required this.name,
    required this.type,
    required this.userId,
    this.supabaseId,
    DateTime? lastModified,
    this.needsSync = true,
    this.isDeleted = false,
  }) {
    this.localUuid = localUuid ?? const Uuid().v4();
    this.lastModified = lastModified ?? DateTime.now().toUtc();
  }

  Map<String, dynamic> toJson() {
    return {
      'local_uuid': localUuid,
      'name': name,
      'type': type,
      'user_id': userId,
      'is_deleted': isDeleted,
      'last_modified': lastModified.toIso8601String(),
    };
  }

  static Category fromJson(Map<String, dynamic> json) {
    return Category(
      localUuid: json['local_uuid'],
      supabaseId: json['id'],
      name: json['name'],
      type: json['type'],
      userId: json['user_id'],
      isDeleted: json['is_deleted'] ?? false,
      lastModified: DateTime.parse(json['last_modified']),
      needsSync: false,
    );
  }
}