// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TransactionAdapter extends TypeAdapter<Transaction> {
  @override
  final int typeId = 3;

  @override
  Transaction read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Transaction(
      localUuid: fields[0] as String?,
      date: fields[1] as DateTime,
      description: fields[2] as String,
      amount: fields[3] as double,
      type: fields[4] as String,
      categoryUuid: fields[5] as String,
      loanUuid: fields[6] as String?,
      userId: fields[7] as String,
      supabaseId: fields[8] as int?,
      lastModified: fields[9] as DateTime?,
      needsSync: fields[10] as bool,
      isDeleted: fields[11] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Transaction obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.localUuid)
      ..writeByte(1)
      ..write(obj.date)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.type)
      ..writeByte(5)
      ..write(obj.categoryUuid)
      ..writeByte(6)
      ..write(obj.loanUuid)
      ..writeByte(7)
      ..write(obj.userId)
      ..writeByte(8)
      ..write(obj.supabaseId)
      ..writeByte(9)
      ..write(obj.lastModified)
      ..writeByte(10)
      ..write(obj.needsSync)
      ..writeByte(11)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TransactionAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
