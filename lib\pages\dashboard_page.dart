import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/capital_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/widgets/sync_status_widget.dart';
import 'package:provider/provider.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final transactionService = Provider.of<TransactionService>(context);
    final loanService = Provider.of<LoanService>(context);
    final budgetService = Provider.of<BudgetService>(context);
    final categoryService = Provider.of<CategoryService>(context);

    // Use the enhanced service methods
    final totalIncome = transactionService.totalCurrentMonthIncome;
    final totalExpenses = transactionService.totalCurrentMonthExpenses;
    final netSavings = transactionService.currentMonthNetSavings;
    final totalActiveDebts = loanService.getTotalActiveDebt();
    final totalActiveCredits = loanService.getTotalActiveCredit();

    return Scaffold(
      appBar: AppBar(
        title: const Text('داشبورد'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        actions: [
          // Sync status and manual sync button
          const SyncStatusWidget(compact: true),
          const SizedBox(width: 8),
          const SyncButton(),
          const SizedBox(width: 8),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(
              icon: Icon(Icons.summarize),
              text: 'خلاصه مالی',
            ),
            Tab(
              icon: Icon(Icons.calendar_month),
              text: 'گزارش ماهانه',
            ),
            Tab(
              icon: Icon(Icons.account_balance),
              text: 'سرمایه کل',
            ),
          ],
        ),
      ),
      backgroundColor: Colors.grey.shade50,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFinancialSummaryTab(totalIncome, totalExpenses, netSavings, totalActiveDebts, totalActiveCredits),
          _buildMonthlyReportTab(transactionService, budgetService, categoryService),
          _buildCapitalTab(transactionService, loanService),
        ],
      ),
    );
  }

  Widget _buildFinancialSummaryTab(double totalIncome, double totalExpenses, double netSavings, double totalActiveDebts, double totalActiveCredits) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Current month header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              'خلاصه مالی ${DateFormatter.getCurrentMonthYear()}',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),

          // Financial summary cards
          _buildFinancialSummarySection(totalIncome, totalExpenses, netSavings),
          const SizedBox(height: 20),

          // Loan summary cards
          _buildLoanSummarySection(totalActiveDebts, totalActiveCredits),
          const SizedBox(height: 20),

          // Quick stats
          _buildQuickStatsSection(totalIncome, totalExpenses, netSavings),
        ],
      ),
    );
  }

  Widget _buildMonthlyReportTab(TransactionService transactionService, BudgetService budgetService, CategoryService categoryService) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Current month header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              'گزارش تفصیلی ${DateFormatter.getCurrentMonthYear()}',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 16),

          // Charts section
          _buildChartsSection(transactionService, budgetService, categoryService),
        ],
      ),
    );
  }

  Widget _buildCapitalTab(TransactionService transactionService, LoanService loanService) {
    return Consumer<CapitalService>(
      builder: (context, capitalService, child) {
        final netWorth = capitalService.netWorth;
        final liquidCapital = capitalService.liquidCapital;
        final totalAssets = capitalService.totalAssets;
        final totalLiabilities = capitalService.totalLiabilities;
        final healthScore = capitalService.financialHealthScore;
        final healthStatus = capitalService.financialHealthStatus;
        final recommendations = capitalService.recommendedActions;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Capital header
              Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: const Text(
                  'وضعیت سرمایه کل',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),

              // Net worth card
              _buildNetWorthCard(netWorth, healthScore, healthStatus),
              const SizedBox(height: 20),

              // Capital breakdown
              _buildEnhancedCapitalBreakdown(liquidCapital, totalAssets, totalLiabilities),
              const SizedBox(height: 20),

              // Financial health and recommendations
              _buildFinancialHealthCard(healthScore, healthStatus, recommendations),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFinancialSummarySection(double totalIncome, double totalExpenses, double netSavings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خلاصه مالی ماه جاری',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'مجموع درآمد',
                totalIncome,
                Colors.green,
                Icons.trending_up,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard(
                'مجموع مصارف',
                totalExpenses,
                Colors.red,
                Icons.trending_down,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildSummaryCard(
          'پس‌انداز خالص',
          netSavings,
          netSavings >= 0 ? Colors.blue : Colors.orange,
          netSavings >= 0 ? Icons.savings : Icons.warning,
          isFullWidth: true,
        ),
      ],
    );
  }

  Widget _buildLoanSummarySection(double totalActiveDebts, double totalActiveCredits) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خلاصه وام‌ها',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'مجموع بدهی‌های فعال',
                totalActiveDebts,
                Colors.orange,
                Icons.money_off,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSummaryCard(
                'مجموع طلب‌های فعال',
                totalActiveCredits,
                Colors.purple,
                Icons.account_balance_wallet,
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getCategoryName(CategoryService categoryService, String categoryId) {
    try {
      return categoryService.categories.firstWhere((c) => c.id == categoryId).name;
    } catch (e) {
      return 'حذف شده'; // "Deleted"
    }
  }

  Widget _buildSummaryCard(String title, double amount, Color color, IconData icon, {bool isFullWidth = false}) {
    final card = Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                CurrencyFormatter.formatWithPersianDigits(amount),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );

    return isFullWidth ? card : Expanded(child: card);
  }

  Widget _buildChartsSection(TransactionService transactionService, BudgetService budgetService, CategoryService categoryService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نمودارها و تحلیل‌ها',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),

        // Expense Pie Chart
        Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'توزیع مصارف بر اساس دسته‌بندی',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 250,
                  child: _buildExpensePieChart(transactionService, categoryService),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Budget vs Actual Bar Chart
        Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'مقایسه بودجه و مصرف واقعی',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  height: 250,
                  child: _buildBudgetBarChart(budgetService, categoryService),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExpensePieChart(TransactionService transactionService, CategoryService categoryService) {
    final expensesByCategory = transactionService.getCurrentMonthExpensesByCategory();

    if (expensesByCategory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.pie_chart_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'هنوز مصرفی ثبت نشده است',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      );
    }

    final colors = [
      Colors.blue,
      Colors.red,
      Colors.green,
      Colors.orange,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
    ];

    return PieChart(
      PieChartData(
        sectionsSpace: 2,
        centerSpaceRadius: 40,
        sections: expensesByCategory.entries.map((entry) {
          final index = expensesByCategory.keys.toList().indexOf(entry.key);
          final color = colors[index % colors.length];
          final categoryName = _getCategoryName(categoryService, entry.key);

          return PieChartSectionData(
            value: entry.value,
            title: '${((entry.value / expensesByCategory.values.fold(0.0, (a, b) => a + b)) * 100).toStringAsFixed(1)}%',
            radius: 80,
            titleStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            color: color,
            badgeWidget: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                  ),
                ],
              ),
              child: Text(
                categoryName,
                style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
              ),
            ),
            badgePositionPercentageOffset: 1.3,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildBudgetBarChart(BudgetService budgetService, CategoryService categoryService) {
    final budgetVsActual = budgetService.getCurrentMonthBudgetVsActual();

    if (budgetVsActual.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bar_chart, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'هنوز بودجه‌ای تعریف نشده است',
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      );
    }

    final categories = budgetVsActual.keys.toList();

    return Column(
      children: [
        // Legend
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLegendItem('بودجه', Colors.blue),
            const SizedBox(width: 20),
            _buildLegendItem('مصرف واقعی', Colors.red),
          ],
        ),
        const SizedBox(height: 16),

        // Bar Chart
        Expanded(
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: budgetVsActual.values
                  .map((data) => [data['budget']!, data['actual']!])
                  .expand((values) => values)
                  .reduce((a, b) => a > b ? a : b) * 1.2,
              barGroups: categories.asMap().entries.map((entry) {
                final index = entry.key;
                final categoryId = entry.value;
                final data = budgetVsActual[categoryId]!;

                return BarChartGroupData(
                  x: index,
                  barRods: [
                    BarChartRodData(
                      toY: data['budget']!,
                      color: Colors.blue,
                      width: 20,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                    BarChartRodData(
                      toY: data['actual']!,
                      color: data['actual']! > data['budget']! ? Colors.red : Colors.green,
                      width: 20,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4),
                      ),
                    ),
                  ],
                );
              }).toList(),
              titlesData: FlTitlesData(
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value.toInt() >= 0 && value.toInt() < categories.length) {
                        final categoryName = _getCategoryName(categoryService, categories[value.toInt()]);
                        return Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            categoryName,
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.center,
                          ),
                        );
                      }
                      return const Text('');
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 60,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        CurrencyFormatter.formatCompactAFN(value),
                        style: const TextStyle(fontSize: 10),
                      );
                    },
                  ),
                ),
                topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              gridData: FlGridData(
                show: true,
                drawVerticalLine: false,
                horizontalInterval: budgetVsActual.values
                    .map((data) => data['budget']!)
                    .reduce((a, b) => a > b ? a : b) / 4,
              ),
              borderData: FlBorderData(show: false),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 8),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }

  Widget _buildQuickStatsSection(double totalIncome, double totalExpenses, double netSavings) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'آمار سریع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickStatItem(
                    'نرخ پس‌انداز',
                    totalIncome > 0 ? '${((netSavings / totalIncome) * 100).toStringAsFixed(1)}%' : '0%',
                    netSavings >= 0 ? Colors.green : Colors.red,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildQuickStatItem(
                    'تعداد تراکنش‌ها',
                    '${Provider.of<TransactionService>(context, listen: false).currentMonthTransactions.length}',
                    Colors.blue,
                    Icons.receipt,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatItem(String label, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCapitalSummaryCard(double totalCapital, double totalActiveDebts, double totalActiveCredits, double netWorth) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              Colors.green.withValues(alpha: 0.1),
              Colors.green.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.account_balance,
                    size: 32,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'ارزش خالص دارایی',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: netWorth >= 0 ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      netWorth >= 0 ? 'مثبت' : 'منفی',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Text(
                CurrencyFormatter.formatWithPersianDigits(netWorth),
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: netWorth >= 0 ? Colors.green : Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCapitalBreakdown(double totalIncome, double totalExpenses, double totalActiveDebts, double totalActiveCredits) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفکیک سرمایه',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildCapitalBreakdownItem(
              'کل درآمدها',
              totalIncome,
              Colors.green,
              Icons.add_circle,
            ),
            const SizedBox(height: 12),
            _buildCapitalBreakdownItem(
              'کل مصارف',
              totalExpenses,
              Colors.red,
              Icons.remove_circle,
            ),
            const SizedBox(height: 12),
            _buildCapitalBreakdownItem(
              'بدهی‌های فعال',
              totalActiveDebts,
              Colors.orange,
              Icons.money_off,
            ),
            const SizedBox(height: 12),
            _buildCapitalBreakdownItem(
              'طلب‌های فعال',
              totalActiveCredits,
              Colors.purple,
              Icons.account_balance_wallet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCapitalBreakdownItem(String label, double amount, Color color, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          CurrencyFormatter.formatWithPersianDigits(amount),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildNetWorthCard(double netWorth, double healthScore, String healthStatus) {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              netWorth >= 0 ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
              netWorth >= 0 ? Colors.green.withValues(alpha: 0.05) : Colors.red.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(
                    Icons.account_balance,
                    size: 32,
                    color: netWorth >= 0 ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'ارزش خالص دارایی',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: _getHealthColor(healthScore),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      healthStatus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Text(
                CurrencyFormatter.formatWithPersianDigits(netWorth),
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: netWorth >= 0 ? Colors.green : Colors.red,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              LinearProgressIndicator(
                value: (healthScore / 100).clamp(0.0, 1.0),
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(_getHealthColor(healthScore)),
                minHeight: 8,
              ),
              const SizedBox(height: 8),
              Text(
                'امتیاز سلامت مالی: ${healthScore.toStringAsFixed(0)}/100',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedCapitalBreakdown(double liquidCapital, double totalAssets, double totalLiabilities) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفکیک دارایی‌ها',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildCapitalBreakdownItem(
              'سرمایه نقد',
              liquidCapital,
              Colors.blue,
              Icons.account_balance_wallet,
            ),
            const SizedBox(height: 12),
            _buildCapitalBreakdownItem(
              'کل دارایی‌ها',
              totalAssets,
              Colors.green,
              Icons.trending_up,
            ),
            const SizedBox(height: 12),
            _buildCapitalBreakdownItem(
              'کل بدهی‌ها',
              totalLiabilities,
              Colors.red,
              Icons.trending_down,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialHealthCard(double healthScore, String healthStatus, List<String> recommendations) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.health_and_safety,
                  color: _getHealthColor(healthScore),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'توصیه‌های مالی',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...recommendations.map((recommendation) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.lightbulb, color: Colors.orange, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      recommendation,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Color _getHealthColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.blue;
    if (score >= 40) return Colors.orange;
    return Colors.red;
  }
}
