import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';

class CapitalService extends ChangeNotifier {
  final TransactionService _transactionService;
  final LoanService _loanService;

  CapitalService(this._transactionService, this._loanService) {
    _transactionService.addListener(_onDataChanged);
    _loanService.addListener(_onDataChanged);
  }

  void _onDataChanged() {
    notifyListeners();
  }

  @override
  void dispose() {
    _transactionService.removeListener(_onDataChanged);
    _loanService.removeListener(_onDataChanged);
    super.dispose();
  }

  /// Get liquid capital (cash on hand)
  double get liquidCapital {
    return _transactionService.totalCapital;
  }

  /// Get total assets (liquid capital + active credits)
  double get totalAssets {
    return liquidCapital + _loanService.getTotalActiveCredit();
  }

  /// Get total liabilities (active debts)
  double get totalLiabilities {
    return _loanService.getTotalActiveDebt();
  }

  /// Get net worth (assets - liabilities)
  double get netWorth {
    return totalAssets - totalLiabilities;
  }

  /// Get capital breakdown
  Map<String, double> get capitalBreakdown {
    return {
      'liquid_capital': liquidCapital,
      'active_credits': _loanService.getTotalActiveCredit(),
      'active_debts': _loanService.getTotalActiveDebt(),
      'net_worth': netWorth,
    };
  }

  /// Get financial health score (0-100)
  double get financialHealthScore {
    final totalIncome = _transactionService.totalIncome;
    final totalExpenses = _transactionService.totalExpenses;
    final totalDebts = _loanService.getTotalActiveDebt();
    
    if (totalIncome == 0) return 0;
    
    // Calculate savings rate
    final savingsRate = (totalIncome - totalExpenses) / totalIncome;
    
    // Calculate debt-to-income ratio
    final debtToIncomeRatio = totalDebts / totalIncome;
    
    // Calculate score (simplified formula)
    double score = 50; // Base score
    
    // Add points for positive savings rate
    if (savingsRate > 0) {
      score += savingsRate * 30; // Up to 30 points for 100% savings rate
    } else {
      score += savingsRate * 50; // Subtract more for negative savings
    }
    
    // Subtract points for high debt ratio
    score -= debtToIncomeRatio * 20; // Subtract up to 20 points for 100% debt ratio
    
    return score.clamp(0, 100);
  }

  /// Get financial health status
  String get financialHealthStatus {
    final score = financialHealthScore;
    if (score >= 80) return 'عالی';
    if (score >= 60) return 'خوب';
    if (score >= 40) return 'متوسط';
    if (score >= 20) return 'ضعیف';
    return 'بحرانی';
  }

  /// Get monthly cash flow trend (last 6 months)
  List<MonthlyCapital> getMonthlyCapitalTrend() {
    final now = DateTime.now();
    final List<MonthlyCapital> trend = [];
    
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthTransactions = _transactionService.transactions.where((t) =>
        t.date.year == month.year && t.date.month == month.month
      ).toList();
      
      final monthIncome = monthTransactions
          .where((t) => t.type == 'درآمد')
          .fold<double>(0.0, (sum, t) => sum + t.amount);
      
      final monthExpenses = monthTransactions
          .where((t) => t.type == 'مصرف')
          .fold<double>(0.0, (sum, t) => sum + t.amount);
      
      trend.add(MonthlyCapital(
        month: month,
        income: monthIncome,
        expenses: monthExpenses,
        netFlow: monthIncome - monthExpenses,
      ));
    }
    
    return trend;
  }

  /// Check if salary was added this month
  bool get salaryAddedThisMonth {
    final now = DateTime.now();
    final thisMonthTransactions = _transactionService.transactions.where((t) =>
      t.date.year == now.year && 
      t.date.month == now.month &&
      t.type == 'درآمد'
    ).toList();
    
    return thisMonthTransactions.any((t) => 
      t.description.toLowerCase().contains('معاش') ||
      t.description.toLowerCase().contains('حقوق') ||
      t.description.toLowerCase().contains('salary')
    );
  }

  /// Get recommended actions based on financial status
  List<String> get recommendedActions {
    final List<String> recommendations = [];
    final score = financialHealthScore;
    final savingsRate = liquidCapital / _transactionService.totalIncome;
    
    if (score < 40) {
      recommendations.add('وضعیت مالی شما نیاز به بهبود دارد');
    }
    
    if (savingsRate < 0.1) {
      recommendations.add('سعی کنید حداقل ۱۰٪ درآمد خود را پس‌انداز کنید');
    }
    
    if (_loanService.getTotalActiveDebt() > _transactionService.totalIncome * 0.3) {
      recommendations.add('بدهی‌های شما زیاد است، برنامه‌ریزی برای کاهش آن‌ها کنید');
    }
    
    if (!salaryAddedThisMonth) {
      recommendations.add('معاش این ماه را ثبت کنید');
    }
    
    if (recommendations.isEmpty) {
      recommendations.add('وضعیت مالی شما مناسب است، ادامه دهید!');
    }
    
    return recommendations;
  }
}

class MonthlyCapital {
  final DateTime month;
  final double income;
  final double expenses;
  final double netFlow;

  MonthlyCapital({
    required this.month,
    required this.income,
    required this.expenses,
    required this.netFlow,
  });
}
