import 'package:flutter/foundation.dart' hide Category;
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:uuid/uuid.dart';

class CategoryService extends ChangeNotifier {
  final Box<Category> _categoryBox = Hive.box<Category>('categories');
  final String _userId; // Assuming a single user for now
  final _uuid = Uuid();

  List<Category> _categories = [];

  CategoryService(this._userId) {
    _loadCategories();
    _initializeDefaultCategories();
  }

  List<Category> get categories => _categories;

  void _loadCategories() {
    // Local-First: Filter out soft-deleted categories
    _categories = _categoryBox.values
        .where((c) => c.userId == _userId && !c.isDeleted)
        .toList();
    notifyListeners();
  }

  void addCategory(String name, String type) {
    final now = DateTime.now();
    final newCategory = Category(
      name: name,
      type: type,
      userId: _userId,
      lastModified: now,
      needsSync: true,
      isDeleted: false,
    );

    _categoryBox.put(newCategory.localUuid, newCategory);
    _loadCategories();
  }

  void updateCategory(Category category, String newName, String newType) {
    // Local-First: Update fields and sync metadata
    category.name = newName;
    category.type = newType;
    category.lastModified = DateTime.now(); // Local-First: track modification
    category.needsSync = true; // Local-First: mark for sync
    category.save();
    _loadCategories();
  }

  void deleteCategory(Category category) {
    // Local-First: Soft delete instead of hard delete
    category.isDeleted = true;
    category.lastModified = DateTime.now();
    category.needsSync = true;
    category.save();
    _loadCategories();
  }

  void _initializeDefaultCategories() {
    if (_categories.isEmpty && _userId.isNotEmpty) {
      // Default income categories
      final defaultIncomeCategories = [
        'معاش',
        'کسب و کار',
        'سرمایه‌گذاری',
        'هدیه',
        'سایر درآمدها',
      ];

      // Default expense categories
      final defaultExpenseCategories = [
        'غذا و نوشیدنی',
        'حمل و نقل',
        'خرید',
        'تفریح',
        'بهداشت و درمان',
        'آموزش',
        'خانه',
        'لباس',
        'سایر مصارف',
      ];

      // Add default income categories
      for (final categoryName in defaultIncomeCategories) {
        final now = DateTime.now();
        final category = Category(
          name: categoryName,
          type: 'درآمد',
          userId: _userId,
          lastModified: now,
          needsSync: true,
          isDeleted: false,
        );
        _categoryBox.put(category.localUuid, category);
      }

      // Add default expense categories
      for (final categoryName in defaultExpenseCategories) {
        final now = DateTime.now();
        final category = Category(
          name: categoryName,
          type: 'مصرف',
          userId: _userId,
          lastModified: now,
          needsSync: true,
          isDeleted: false,
        );
        _categoryBox.put(category.localUuid, category);
      }

      _loadCategories();
    }
  }
}
