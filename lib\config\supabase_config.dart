/// Supabase Configuration
/// 
/// To set up your Supabase project:
/// 1. Go to https://supabase.com and create a new project
/// 2. Get your project URL and anon key from Settings > API
/// 3. Replace the placeholder values below
/// 4. Create the following tables in your Supabase database:

class SupabaseConfig {
  // TODO: Replace with your actual Supabase credentials
  static const String url = 'https://wakwfevrgaqoqlmjsnzm.supabase.co';
  static const String anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indha3dmZXZyZ2Fxb3FsbWpzbnptIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDE4NDcsImV4cCI6MjA2OTI3Nzg0N30.mf2eGuly9PGQPxdc8JkMxQwoLv2ftEgf6FhdBughGpI';
  
  // Table names
  static const String usersTable = 'users';
  static const String categoriesTable = 'categories';
  static const String transactionsTable = 'transactions';
  static const String budgetsTable = 'budgets';
  static const String loansTable = 'loans';
}

/// SQL Schema for Supabase Tables
/// 
/// Run these SQL commands in your Supabase SQL editor:

const String supabaseSchema = '''
-- Enable Row Level Security
ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

-- Users table
CREATE TABLE users (
  local_uuid TEXT PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT NOT NULL,
  last_modified TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Categories table
CREATE TABLE categories (
  local_uuid TEXT PRIMARY KEY,
  id TEXT NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  last_modified TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Transactions table
CREATE TABLE transactions (
  local_uuid TEXT PRIMARY KEY,
  id TEXT NOT NULL,
  date TIMESTAMPTZ NOT NULL,
  description TEXT NOT NULL,
  amount DECIMAL NOT NULL,
  type TEXT NOT NULL,
  category_id TEXT NOT NULL,
  loan_id TEXT,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  last_modified TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Budgets table
CREATE TABLE budgets (
  local_uuid TEXT PRIMARY KEY,
  id TEXT NOT NULL,
  period TIMESTAMPTZ NOT NULL,
  amount DECIMAL NOT NULL,
  category_id TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  last_modified TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Loans table
CREATE TABLE loans (
  local_uuid TEXT PRIMARY KEY,
  id TEXT NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL,
  person TEXT NOT NULL,
  initial_amount DECIMAL NOT NULL,
  start_date TIMESTAMPTZ NOT NULL,
  status TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  last_modified TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);

-- Row Level Security Policies
CREATE POLICY "Users can only access their own data" ON users
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own categories" ON categories
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own transactions" ON transactions
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own budgets" ON budgets
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own loans" ON loans
  FOR ALL USING (auth.uid() = user_id);

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE loans ENABLE ROW LEVEL SECURITY;

-- Indexes for better performance
CREATE INDEX idx_categories_user_id ON categories(user_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_budgets_user_id ON budgets(user_id);
CREATE INDEX idx_loans_user_id ON loans(user_id);
CREATE INDEX idx_transactions_last_modified ON transactions(last_modified);
CREATE INDEX idx_categories_last_modified ON categories(last_modified);
CREATE INDEX idx_budgets_last_modified ON budgets(last_modified);
CREATE INDEX idx_loans_last_modified ON loans(last_modified);
''';
