import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:uuid/uuid.dart';

class TransactionService extends ChangeNotifier {
  final Box<Transaction> _transactionBox = Hive.box<Transaction>('transactions');
  final String _userId;
  final _uuid = Uuid();
  LoanService? _loanService;

  List<Transaction> _transactions = [];

  TransactionService(this._userId) {
    _loadTransactions();
  }

  void setLoanService(LoanService loanService) {
    _loanService = loanService;
  }

  List<Transaction> get transactions => _transactions;

  /// Get transactions for current month
  List<Transaction> get currentMonthTransactions {
    final now = DateTime.now();
    return _transactions.where((t) =>
      t.date.year == now.year && t.date.month == now.month
    ).toList();
  }

  /// Get income transactions for current month
  List<Transaction> get currentMonthIncome {
    return currentMonthTransactions.where((t) => t.type == 'درآمد').toList();
  }

  /// Get expense transactions for current month
  List<Transaction> get currentMonthExpenses {
    return currentMonthTransactions.where((t) => t.type == 'مصرف').toList();
  }

  /// Get total income for current month
  double get totalCurrentMonthIncome {
    return currentMonthIncome.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses for current month
  double get totalCurrentMonthExpenses {
    return currentMonthExpenses.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get net savings for current month
  double get currentMonthNetSavings {
    return totalCurrentMonthIncome - totalCurrentMonthExpenses;
  }

  /// Get transactions for a specific loan
  List<Transaction> getTransactionsForLoan(String loanUuid) {
    return _transactions.where((t) => t.loanUuid == loanUuid).toList();
  }

  void _loadTransactions() {
    // Local-First: Filter out soft-deleted transactions
    _transactions = _transactionBox.values
        .where((t) => t.userId == _userId && !t.isDeleted)
        .toList();
    notifyListeners();
  }

  void addTransaction(String description, double amount, String type, String categoryUuid, {String? loanUuid, DateTime? date}) {
    final now = DateTime.now();
    final newTransaction = Transaction(
      date: date ?? now,
      description: description,
      amount: amount,
      type: type,
      categoryUuid: categoryUuid,
      loanUuid: loanUuid,
      userId: _userId,
      lastModified: now,
      needsSync: true,
      isDeleted: false,
    );

    _transactionBox.put(newTransaction.localUuid, newTransaction);
    _loadTransactions();

    // Update loan statuses if this transaction is linked to a loan
    if (loanUuid != null && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  void updateTransaction(
    Transaction transaction,
    String description,
    double amount,
    String type,
    String categoryUuid, {
    String? loanUuid,
    DateTime? date,
  }) {
    final oldLoanUuid = transaction.loanUuid;

    // Local-First: Update fields and sync metadata
    transaction.description = description;
    transaction.amount = amount;
    transaction.type = type;
    transaction.categoryUuid = categoryUuid;
    transaction.loanUuid = loanUuid;
    transaction.date = date ?? transaction.date;
    transaction.lastModified = DateTime.now(); // Local-First: track modification
    transaction.needsSync = true; // Local-First: mark for sync
    transaction.save();

    _loadTransactions();

    // Update loan statuses if this transaction was linked to a loan
    if ((oldLoanUuid != null || loanUuid != null) && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  void deleteTransaction(Transaction transaction) {
    final loanUuid = transaction.loanUuid;

    // Local-First: Soft delete instead of hard delete
    transaction.isDeleted = true;
    transaction.lastModified = DateTime.now();
    transaction.needsSync = true;
    transaction.save();

    _loadTransactions();

    // Update loan statuses if this transaction was linked to a loan
    if (loanUuid != null && _loanService != null) {
      _loanService!.updateLoanStatuses();
    }
  }

  /// Get transactions by category for current month
  Map<String, double> getCurrentMonthExpensesByCategory() {
    final expenses = currentMonthExpenses;
    final Map<String, double> categoryTotals = {};

    for (final transaction in expenses) {
      categoryTotals[transaction.categoryUuid] =
          (categoryTotals[transaction.categoryUuid] ?? 0.0) + transaction.amount;
    }

    return categoryTotals;
  }

  /// Get total capital (all time income - all time expenses)
  double get totalCapital {
    final totalIncome = _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final totalExpenses = _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    return totalIncome - totalExpenses;
  }

  /// Get total income (all time)
  double get totalIncome {
    return _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses (all time)
  double get totalExpenses {
    return _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get salary transactions (assuming salary is in a specific category)
  List<Transaction> get salaryTransactions {
    return _transactions.where((t) =>
      t.type == 'درآمد' &&
      t.description.toLowerCase().contains('معاش') ||
      t.description.toLowerCase().contains('حقوق') ||
      t.description.toLowerCase().contains('salary')
    ).toList();
  }

  /// Get total salary received
  double get totalSalary {
    return salaryTransactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }
}
