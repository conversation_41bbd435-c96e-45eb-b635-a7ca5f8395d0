import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'loan.g.dart';

@HiveType(typeId: 1) // Note: I corrected the typeId to 1 as it was in your original code
class <PERSON>an extends HiveObject {
  @HiveField(0)
  late String localUuid;

  @HiveField(1)
  late String name;

  @HiveField(2)
  late String type;

  @HiveField(3)
  late String person;

  @HiveField(4)
  late double initialAmount;

  @HiveField(5)
  late DateTime startDate;

  @HiveField(6)
  late String status;

  @HiveField(7)
  late String userId;

  @HiveField(8)
  int? supabaseId;

  @HiveField(9)
  late DateTime lastModified;

  @HiveField(10)
  bool needsSync;

  @HiveField(11)
  bool isDeleted;

  Loan({
    String? localUuid,
    required this.name,
    required this.type,
    required this.person,
    required this.initialAmount,
    required this.startDate,
    required this.status,
    required this.userId,
    this.supabaseId,
    DateTime? lastModified,
    this.needsSync = true,
    this.isDeleted = false,
  }) {
    this.localUuid = localUuid ?? const Uuid().v4();
    this.lastModified = lastModified ?? DateTime.now().toUtc();
  }

  Map<String, dynamic> toJson() {
    return {
      'local_uuid': localUuid,
      'name': name,
      'type': type,
      'person': person,
      'initial_amount': initialAmount,
      'start_date': startDate.toIso8601String(),
      'status': status,
      'user_id': userId,
      'is_deleted': isDeleted,
      'last_modified': lastModified.toIso8601String(),
    };
  }

  static Loan fromJson(Map<String, dynamic> json) {
    return Loan(
      localUuid: json['local_uuid'],
      supabaseId: json['id'],
      name: json['name'],
      type: json['type'],
      person: json['person'],
      initialAmount: (json['initial_amount'] as num).toDouble(),
      startDate: DateTime.parse(json['start_date']),
      status: json['status'],
      userId: json['user_id'],
      isDeleted: json['is_deleted'] ?? false,
      lastModified: DateTime.parse(json['last_modified']),
      needsSync: false,
    );
  }
}