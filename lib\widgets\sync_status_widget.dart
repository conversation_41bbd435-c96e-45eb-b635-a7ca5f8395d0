import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/sync_service.dart';
import '../services/connectivity_service.dart';

class SyncStatusWidget extends StatelessWidget {
  final bool showText;
  final bool compact;

  const SyncStatusWidget({
    super.key,
    this.showText = true,
    this.compact = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<SyncService, ConnectivityService>(
      builder: (context, syncService, connectivityService, child) {
        return _buildSyncStatus(context, syncService, connectivityService);
      },
    );
  }

  Widget _buildSyncStatus(
    BuildContext context,
    SyncService syncService,
    ConnectivityService connectivityService,
  ) {
    final isOnline = connectivityService.isOnline;
    final isSyncing = syncService.isSyncing;
    final lastSyncTime = syncService.lastSyncTime;
    final syncStatus = syncService.syncStatus;

    if (compact) {
      return _buildCompactStatus(isOnline, isSyncing, syncStatus);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getStatusColor(isOnline, isSyncing, syncStatus).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getStatusColor(isOnline, isSyncing, syncStatus).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildStatusIcon(isOnline, isSyncing, syncStatus),
          if (showText) ...[
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getStatusText(isOnline, isSyncing, syncStatus),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: _getStatusColor(isOnline, isSyncing, syncStatus),
                  ),
                ),
                if (lastSyncTime != null && !isSyncing)
                  Text(
                    _getLastSyncText(lastSyncTime),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactStatus(bool isOnline, bool isSyncing, String syncStatus) {
    return Container(
      width: 12,
      height: 12,
      decoration: BoxDecoration(
        color: _getStatusColor(isOnline, isSyncing, syncStatus),
        shape: BoxShape.circle,
      ),
      child: isSyncing
          ? const SizedBox(
              width: 8,
              height: 8,
              child: CircularProgressIndicator(
                strokeWidth: 1,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : null,
    );
  }

  Widget _buildStatusIcon(bool isOnline, bool isSyncing, String syncStatus) {
    if (isSyncing) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
      );
    }

    IconData iconData;
    if (!isOnline) {
      iconData = Icons.cloud_off;
    } else if (syncStatus.startsWith('error')) {
      iconData = Icons.error_outline;
    } else if (syncStatus == 'completed') {
      iconData = Icons.cloud_done;
    } else {
      iconData = Icons.cloud_queue;
    }

    return Icon(
      iconData,
      size: 16,
      color: _getStatusColor(isOnline, isSyncing, syncStatus),
    );
  }

  Color _getStatusColor(bool isOnline, bool isSyncing, String syncStatus) {
    if (isSyncing) return Colors.blue;
    if (!isOnline) return Colors.grey;
    if (syncStatus.startsWith('error')) return Colors.red;
    if (syncStatus == 'completed') return Colors.green;
    return Colors.orange;
  }

  String _getStatusText(bool isOnline, bool isSyncing, String syncStatus) {
    if (isSyncing) return 'در حال همگام‌سازی...';
    if (!isOnline) return 'آفلاین';
    if (syncStatus.startsWith('error')) return 'خطا در همگام‌سازی';
    if (syncStatus == 'completed') return 'همگام‌سازی شده';
    return 'آماده همگام‌سازی';
  }

  String _getLastSyncText(DateTime lastSync) {
    final now = DateTime.now();
    final difference = now.difference(lastSync);

    if (difference.inMinutes < 1) {
      return 'همین الان';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} دقیقه پیش';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ساعت پیش';
    } else {
      return '${difference.inDays} روز پیش';
    }
  }
}

class SyncButton extends StatelessWidget {
  const SyncButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<SyncService, ConnectivityService>(
      builder: (context, syncService, connectivityService, child) {
        final canSync = connectivityService.isOnline && !syncService.isSyncing;
        
        return IconButton(
          onPressed: canSync
              ? () async {
                  final success = await syncService.sync();
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success 
                              ? 'همگام‌سازی با موفقیت انجام شد'
                              : 'خطا در همگام‌سازی',
                        ),
                        backgroundColor: success ? Colors.green : Colors.red,
                      ),
                    );
                  }
                }
              : null,
          icon: syncService.isSyncing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.sync),
          tooltip: canSync ? 'همگام‌سازی دستی' : 'همگام‌سازی غیرفعال',
        );
      },
    );
  }
}

class ConnectivityIndicator extends StatelessWidget {
  const ConnectivityIndicator({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ConnectivityService>(
      builder: (context, connectivityService, child) {
        if (connectivityService.isOnline) {
          return const SizedBox.shrink(); // Hide when online
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          color: Colors.red[100],
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.cloud_off, size: 16, color: Colors.red[700]),
              const SizedBox(width: 8),
              Text(
                'اتصال اینترنت قطع است - حالت آفلاین',
                style: TextStyle(
                  color: Colors.red[700],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
