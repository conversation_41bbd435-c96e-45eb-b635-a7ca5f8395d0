import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

class SupabaseService {
  static SupabaseService? _instance;
  static SupabaseService get instance => _instance ??= SupabaseService._();

  SupabaseService._();

  SupabaseClient? _client;
  SupabaseClient get client {
    if (_client == null) {
      throw Exception('Supabase not initialized. Call initialize() first.');
    }
    return _client!;
  }

  bool get isInitialized => _client != null;

  /// Initialize Supabase client
  Future<void> initialize() async {
    if (_client != null) return; // Already initialized

    await Supabase.initialize(
      url: SupabaseConfig.url,
      anonKey: SupabaseConfig.anonKey,
    );

    _client = Supabase.instance.client;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _client?.auth.currentUser != null;

  /// Get current user ID
  String? get currentUserId => _client?.auth.currentUser?.id;

  /// Sign in with email and password
  Future<AuthResponse> signIn(String email, String password) async {
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  /// Sign up with email and password
  Future<AuthResponse> signUp(String email, String password) async {
    return await client.auth.signUp(
      email: email,
      password: password,
    );
  }

  /// Sign out
  Future<void> signOut() async {
    await client.auth.signOut();
  }

  /// Get authenticated user's data from a table
  Future<List<Map<String, dynamic>>> getUserData(String tableName) async {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    final response = await client
        .from(tableName)
        .select()
        .eq('user_id', currentUserId!);

    return response;
  }

  /// Upsert data to a table (handles both insert and update)
  Future<void> upsertData(String tableName, List<Map<String, dynamic>> data) async {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    await client
        .from(tableName)
        .upsert(data, onConflict: 'local_uuid');
  }

  /// Get data modified after a specific timestamp
  Future<List<Map<String, dynamic>>> getModifiedData(
    String tableName, 
    DateTime lastSync,
  ) async {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    final response = await client
        .from(tableName)
        .select()
        .eq('user_id', currentUserId!)
        .gt('last_modified', lastSync.toIso8601String());

    return response;
  }
}
