import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/config/supabase_config.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/home_page.dart';
import 'package:my_fincance_app/services/auth_service.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/sync_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.url,
    anonKey: SupabaseConfig.anonKey,
  );

  // Initialize Hive
  await Hive.initFlutter();
  Hive.registerAdapter(CategoryAdapter());
  Hive.registerAdapter(LoanAdapter());
  Hive.registerAdapter(TransactionAdapter());
  Hive.registerAdapter(BudgetAdapter());

  await Hive.openBox<Category>('categories');
  await Hive.openBox<Loan>('loans');
  await Hive.openBox<Transaction>('transactions');
  await Hive.openBox<Budget>('budgets');

  // Initialize SyncService
  await SyncService.instance.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthService()),
        ChangeNotifierProvider(create: (_) => SyncService.instance),

        ChangeNotifierProxyProvider<AuthService, CategoryService>(
          create: (context) => CategoryService(''),
          update: (_, auth, __) => CategoryService(auth.currentUserId ?? ''),
        ),
        ChangeNotifierProxyProvider<AuthService, LoanService>(
          create: (context) => LoanService(''),
          update: (_, auth, __) => LoanService(auth.currentUserId ?? ''),
        ),
        ChangeNotifierProxyProvider<AuthService, TransactionService>(
          create: (context) => TransactionService(''),
          update: (_, auth, __) => TransactionService(auth.currentUserId ?? ''),
        ),
        ChangeNotifierProxyProvider<AuthService, BudgetService>(
          create: (context) => BudgetService(''),
          update: (_, auth, __) => BudgetService(auth.currentUserId ?? ''),
        ),
      ],
      child: MaterialApp(
        title: 'مرکز مالی من',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          textTheme: GoogleFonts.lalezarTextTheme(Theme.of(context).textTheme),
        ),
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [Locale('fa', '')],
        locale: const Locale('fa', ''),
        home: const AuthHandler(),
      ),
    );
  }
}

class AuthHandler extends StatefulWidget {
  const AuthHandler({super.key});

  @override
  State<AuthHandler> createState() => _AuthHandlerState();
}

class _AuthHandlerState extends State<AuthHandler> {
  bool _hasTriggeredSync = false;

  @override
  Widget build(BuildContext context) {
    // This widget will listen to auth changes and show the correct page.
    return StreamBuilder<AuthState>(
      stream: Supabase.instance.client.auth.onAuthStateChange,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data?.session != null) {
          // User is logged in, trigger initial sync if not done yet
          if (!_hasTriggeredSync) {
            _hasTriggeredSync = true;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              SyncService.instance.syncAll();
            });
          }
          return const HomePage();
        } else {
          // User is not logged in, reset sync flag
          _hasTriggeredSync = false;
          return const LoginPage();
        }
      },
    );
  }
}
