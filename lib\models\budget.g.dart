// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'budget.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BudgetAdapter extends TypeAdapter<Budget> {
  @override
  final int typeId = 4;

  @override
  Budget read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Budget(
      localUuid: fields[0] as String?,
      period: fields[1] as DateTime,
      amount: fields[2] as double,
      categoryUuid: fields[3] as String,
      userId: fields[4] as String,
      supabaseId: fields[5] as int?,
      lastModified: fields[6] as DateTime?,
      needsSync: fields[7] as bool,
      isDeleted: fields[8] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, Budget obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.localUuid)
      ..writeByte(1)
      ..write(obj.period)
      ..writeByte(2)
      ..write(obj.amount)
      ..writeByte(3)
      ..write(obj.categoryUuid)
      ..writeByte(4)
      ..write(obj.userId)
      ..writeByte(5)
      ..write(obj.supabaseId)
      ..writeByte(6)
      ..write(obj.lastModified)
      ..writeByte(7)
      ..write(obj.needsSync)
      ..writeByte(8)
      ..write(obj.isDeleted);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BudgetAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
