import 'package:flutter/foundation.dart';
import 'package:hive/hive.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:uuid/uuid.dart';

class LoanService extends ChangeNotifier {
  final Box<Loan> _loanBox = Hive.box<Loan>('loans');
  final Box<Transaction> _transactionBox = Hive.box<Transaction>('transactions');
  final String _userId;
  final _uuid = Uuid();

  List<Loan> _loans = [];

  LoanService(this._userId) {
    _loadLoans();
  }

  List<Loan> get loans => _loans;
  List<Loan> get debtLoans => _loans.where((l) => l.type == 'بدهی').toList();
  List<Loan> get creditLoans => _loans.where((l) => l.type == 'طلب').toList();
  List<Loan> get activeLoans => _loans.where((l) => l.status == 'فعال').toList();

  void _loadLoans() {
    // Local-First: Filter out soft-deleted loans
    _loans = _loanBox.values
        .where((l) => l.userId == _userId && !l.isDeleted)
        .toList();
    _updateLoanStatuses();
    notifyListeners();
  }

  /// Calculate remaining balance for a specific loan
  double getRemainingBalance(String loanUuid) {
    final loanTransactions = _transactionBox.values
        .where((t) => t.userId == _userId && t.loanUuid == loanUuid && !t.isDeleted) // Local-First: exclude soft-deleted
        .toList();

    final totalPaid = loanTransactions.fold<double>(0.0, (sum, transaction) => sum + transaction.amount);
    final loan = _loans.firstWhere((l) => l.localUuid == loanUuid);
    return loan.initialAmount - totalPaid;
  }

  /// Get total active debt amount
  double getTotalActiveDebt() {
    return debtLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.localUuid));
  }

  /// Get total active credit amount
  double getTotalActiveCredit() {
    return creditLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.localUuid));
  }

  /// Update loan statuses based on remaining balances
  void _updateLoanStatuses() {
    bool hasUpdates = false;
    for (final loan in _loans) {
      if (loan.status == 'فعال') {
        final remainingBalance = getRemainingBalance(loan.localUuid);
        if (remainingBalance <= 0) {
          loan.status = 'تمام شده';
          loan.lastModified = DateTime.now();
          loan.needsSync = true;
          loan.save();
          hasUpdates = true;
        }
      }
    }
    if (hasUpdates) {
      _loadLoans();
    }
  }

  /// Get loan repayment progress (0.0 to 1.0)
  double getLoanProgress(String loanUuid) {
    final loan = _loans.firstWhere((l) => l.localUuid == loanUuid);
    final remainingBalance = getRemainingBalance(loanUuid);
    final paidAmount = loan.initialAmount - remainingBalance;
    return loan.initialAmount > 0 ? (paidAmount / loan.initialAmount).clamp(0.0, 1.0) : 0.0;
  }

  void addLoan(String name, String type, String person, double initialAmount, DateTime startDate) {
    final now = DateTime.now();
    final newLoan = Loan(
      name: name,
      type: type,
      person: person,
      initialAmount: initialAmount,
      startDate: startDate,
      status: 'فعال',
      userId: _userId,
      lastModified: now,
      needsSync: true,
      isDeleted: false,
    );

    _loanBox.put(newLoan.localUuid, newLoan);
    _loadLoans();
  }

  /// Force update loan statuses (call this after adding transactions)
  void updateLoanStatuses() {
    _updateLoanStatuses();
    notifyListeners();
  }

  void updateLoan(Loan loan, String newName, String newType, String newPerson, double newInitialAmount, DateTime newStartDate) {
    // Local-First: Update fields and sync metadata
    loan.name = newName;
    loan.type = newType;
    loan.person = newPerson;
    loan.initialAmount = newInitialAmount;
    loan.startDate = newStartDate;
    loan.lastModified = DateTime.now(); // Local-First: track modification
    loan.needsSync = true; // Local-First: mark for sync
    loan.save();
    _loadLoans();
  }

  void deleteLoan(Loan loan) {
    // Local-First: Soft delete instead of hard delete
    loan.isDeleted = true;
    loan.lastModified = DateTime.now();
    loan.needsSync = true;
    loan.save();
    _loadLoans();
  }
}
