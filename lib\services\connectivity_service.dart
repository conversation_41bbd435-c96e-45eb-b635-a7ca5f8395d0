import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'sync_service.dart';

class ConnectivityService extends ChangeNotifier {
  static ConnectivityService? _instance;
  static ConnectivityService get instance => _instance ??= ConnectivityService._();
  
  ConnectivityService._();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  List<ConnectivityResult> _connectionStatus = [ConnectivityResult.none];
  bool _isOnline = false;
  bool _wasOffline = false;

  List<ConnectivityResult> get connectionStatus => _connectionStatus;
  bool get isOnline => _isOnline;
  bool get hasInternetConnection => _isOnline;

  /// Initialize connectivity monitoring
  Future<void> initialize() async {
    // Check initial connectivity status
    _connectionStatus = await _connectivity.checkConnectivity();
    _isOnline = !_connectionStatus.contains(ConnectivityResult.none);

    // Listen to connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
      onError: (error) {
        debugPrint('Connectivity error: $error');
      },
    );
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    final wasOnline = _isOnline;
    _connectionStatus = results;
    _isOnline = !results.contains(ConnectivityResult.none);

    debugPrint('Connectivity changed: $results (Online: $_isOnline)');

    // Notify listeners about connectivity change
    notifyListeners();

    // If we just came back online, trigger sync
    if (!wasOnline && _isOnline) {
      _onConnectionRestored();
    } else if (wasOnline && !_isOnline) {
      _onConnectionLost();
    }
  }

  /// Called when internet connection is restored
  void _onConnectionRestored() {
    debugPrint('Internet connection restored - triggering sync');
    _triggerAutoSync();
  }

  /// Called when internet connection is lost
  void _onConnectionLost() {
    debugPrint('Internet connection lost');
    _wasOffline = true;
  }

  /// Trigger automatic sync when conditions are met
  void _triggerAutoSync() {
    if (_isOnline) {
      // Delay sync slightly to ensure connection is stable
      Timer(const Duration(seconds: 2), () {
        SyncService.instance.sync().then((success) {
          if (success) {
            debugPrint('Auto-sync completed successfully');
          } else {
            debugPrint('Auto-sync failed');
          }
        });
      });
    }
  }

  /// Check if device has internet connectivity
  Future<bool> hasInternetAccess() async {
    try {
      final results = await _connectivity.checkConnectivity();
      return !results.contains(ConnectivityResult.none);
    } catch (e) {
      debugPrint('Error checking internet access: $e');
      return false;
    }
  }

  /// Get connection type as string
  String get connectionTypeString {
    if (_connectionStatus.contains(ConnectivityResult.wifi)) {
      return 'WiFi';
    } else if (_connectionStatus.contains(ConnectivityResult.mobile)) {
      return 'Mobile Data';
    } else if (_connectionStatus.contains(ConnectivityResult.ethernet)) {
      return 'Ethernet';
    } else if (_connectionStatus.contains(ConnectivityResult.bluetooth)) {
      return 'Bluetooth';
    } else if (_connectionStatus.contains(ConnectivityResult.vpn)) {
      return 'VPN';
    } else if (_connectionStatus.contains(ConnectivityResult.other)) {
      return 'Other';
    } else {
      return 'No Connection';
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
