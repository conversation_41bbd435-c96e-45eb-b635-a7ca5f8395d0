import 'package:hive/hive.dart';
import 'package:uuid/uuid.dart';

part 'transaction.g.dart';

@HiveType(typeId: 3)
class Transaction extends HiveObject {
  @HiveField(0)
  late String localUuid;

  @HiveField(1)
  late DateTime date;

  @HiveField(2)
  late String description;

  @HiveField(3)
  late double amount;

  @HiveField(4)
  late String type;

  @HiveField(5)
  late String categoryUuid; // Foreign key to Category's localUuid

  @HiveField(6)
  String? loanUuid; // Foreign key to <PERSON><PERSON>'s localUuid

  @HiveField(7)
  late String userId;

  @HiveField(8)
  int? supabaseId;

  @HiveField(9)
  late DateTime lastModified;

  @HiveField(10)
  bool needsSync;

  @HiveField(11)
  bool isDeleted;

  Transaction({
    String? localUuid,
    required this.date,
    required this.description,
    required this.amount,
    required this.type,
    required this.categoryUuid,
    this.loanUuid,
    required this.userId,
    this.supabaseId,
    DateTime? lastModified,
    this.needsSync = true,
    this.isDeleted = false,
  }) {
    this.localUuid = localUuid ?? const Uuid().v4();
    this.lastModified = lastModified ?? DateTime.now().toUtc();
  }

  Map<String, dynamic> toJson({int? categorySupabaseId, int? loanSupabaseId}) {
    return {
      'local_uuid': localUuid,
      'date': date.toIso8601String(),
      'description': description,
      'amount': amount,
      'type': type,
      'category_id': categorySupabaseId,
      'loan_id': loanSupabaseId,
      'user_id': userId,
      'is_deleted': isDeleted,
      'last_modified': lastModified.toIso8601String(),
    };
  }

  static Transaction? fromJson(Map<String, dynamic> json) {
    // We need a way to find the local UUID from the remote Supabase ID
    // This will be handled in the SyncService
    return null; // This method is not suitable for direct use anymore
  }
}