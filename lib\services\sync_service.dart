import 'package:flutter/foundation.dart' hide Category;
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/transaction.dart';
import '../models/category.dart';
import '../models/budget.dart';
import '../models/loan.dart';
import '../config/supabase_config.dart';
import 'supabase_service.dart';

class SyncService extends ChangeNotifier {
  static SyncService? _instance;
  static SyncService get instance => _instance ??= SyncService._();
  
  SyncService._();

  final SupabaseService _supabaseService = SupabaseService.instance;
  bool _isSyncing = false;
  String _syncStatus = 'idle';
  DateTime? _lastSyncTime;

  // Hive boxes
  late Box<Transaction> _transactionBox;
  late Box<Category> _categoryBox;
  late Box<Budget> _budgetBox;
  late Box<Loan> _loanBox;

  bool get isSyncing => _isSyncing;
  String get syncStatus => _syncStatus;
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Initialize the sync service
  Future<void> initialize() async {
    _transactionBox = Hive.box<Transaction>('transactions');
    _categoryBox = Hive.box<Category>('categories');
    _budgetBox = Hive.box<Budget>('budgets');
    _loanBox = Hive.box<Loan>('loans');

    // Load last sync time
    final prefs = await SharedPreferences.getInstance();
    final lastSyncString = prefs.getString('last_sync_time');
    if (lastSyncString != null) {
      _lastSyncTime = DateTime.parse(lastSyncString);
    }

    // Listen to connectivity changes
    Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) {
      if (!results.contains(ConnectivityResult.none)) {
        _autoSync();
      }
    });
  }

  /// Main sync method - pulls then pushes
  Future<bool> sync() async {
    if (_isSyncing || !_supabaseService.isAuthenticated) {
      return false;
    }

    _isSyncing = true;
    _syncStatus = 'syncing';
    notifyListeners();

    try {
      // Step 1: Pull remote changes first
      await _pullRemoteChanges();
      
      // Step 2: Push local changes
      await _pushLocalChanges();

      // Update last sync time
      _lastSyncTime = DateTime.now();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_sync_time', _lastSyncTime!.toIso8601String());

      _syncStatus = 'completed';
      return true;
    } catch (e) {
      _syncStatus = 'error: ${e.toString()}';
      debugPrint('Sync error: $e');
      return false;
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  /// Pull remote changes from Supabase (Delta Sync)
  Future<void> _pullRemoteChanges() async {
    final lastSync = _lastSyncTime ?? DateTime(2000); // Very old date if never synced

    // Pull changes for each table - order matters for foreign keys
    await _pullTableChanges<Category>(
      SupabaseConfig.categoriesTable,
      _categoryBox,
      (json) => Category.fromJson(json),
    );

    await _pullTableChanges<Loan>(
      SupabaseConfig.loansTable,
      _loanBox,
      (json) => Loan.fromJson(json),
    );

    await _pullTableChanges<Transaction>(
      SupabaseConfig.transactionsTable,
      _transactionBox,
      (json) => _transactionFromJson(json),
    );

    await _pullTableChanges<Budget>(
      SupabaseConfig.budgetsTable,
      _budgetBox,
      (json) => _budgetFromJson(json),
    );
  }

  /// Pull changes for a specific table
  Future<void> _pullTableChanges<T extends HiveObject>(
    String tableName,
    Box<T> box,
    T Function(Map<String, dynamic>) fromJson,
  ) async {
    final lastSync = _lastSyncTime ?? DateTime(2000);
    
    try {
      final remoteData = await _supabaseService.getModifiedData(tableName, lastSync);
      
      for (final remoteRecord in remoteData) {
        final localUuid = remoteRecord['local_uuid'] as String;
        final remoteLastModified = DateTime.parse(remoteRecord['last_modified']);
        
        // Find local record
        T? localRecord;
        try {
          localRecord = box.values.firstWhere(
            (item) => _getLocalUuid(item) == localUuid,
          );
        } catch (e) {
          localRecord = null;
        }

        if (localRecord == null) {
          // Record doesn't exist locally, create it
          if (remoteRecord['is_deleted'] == false) {
            final newRecord = fromJson(remoteRecord);
            await box.put(_getLocalUuid(newRecord), newRecord);
          }
        } else {
          // Record exists locally, check for conflicts
          final localLastModified = _getLastModified(localRecord);
          
          if (remoteLastModified.isAfter(localLastModified)) {
            // Remote is newer, update local
            if (remoteRecord['is_deleted'] == true) {
              await localRecord.delete();
            } else {
              final updatedRecord = fromJson(remoteRecord);
              await box.put(localUuid, updatedRecord);
            }
          }
          // If local is newer or same, keep local version
        }
      }
    } catch (e) {
      debugPrint('Error pulling $tableName: $e');
      rethrow;
    }
  }

  /// Push local changes to Supabase
  Future<void> _pushLocalChanges() async {
    // Push in correct order: independent tables first, then dependent tables
    await _pushTableChanges(SupabaseConfig.categoriesTable, _categoryBox);
    await _pushTableChanges(SupabaseConfig.loansTable, _loanBox);
    await _pushTableChanges(SupabaseConfig.transactionsTable, _transactionBox);
    await _pushTableChanges(SupabaseConfig.budgetsTable, _budgetBox);
  }

  /// Push changes for a specific table
  Future<void> _pushTableChanges<T extends HiveObject>(
    String tableName,
    Box<T> box,
  ) async {
    try {
      // Get all records that need sync
      final recordsToSync = box.values.where((record) => _getNeedsSync(record)).toList();

      if (recordsToSync.isEmpty) return;

      // Convert to JSON for Supabase with proper foreign key handling
      final jsonDataList = <Map<String, dynamic>>[];

      for (final record in recordsToSync) {
        final json = await _toJsonWithForeignKeys(record);
        if (json != null) {
          json['user_id'] = _supabaseService.currentUserId;
          jsonDataList.add(json);
        }
      }

      if (jsonDataList.isEmpty) return;

      // Upsert to Supabase
      await _supabaseService.upsertData(tableName, jsonDataList);

      // Mark records as synced (only those that were actually sent)
      for (int i = 0; i < recordsToSync.length; i++) {
        if (i < jsonDataList.length) {
          _setNeedsSync(recordsToSync[i], false);
          await recordsToSync[i].save();
        }
      }
    } catch (e) {
      debugPrint('Error pushing $tableName: $e');
      rethrow;
    }
  }

  /// Auto sync when conditions are met
  Future<void> _autoSync() async {
    // Only auto-sync if we haven't synced recently
    if (_lastSyncTime != null &&
        DateTime.now().difference(_lastSyncTime!).inMinutes < 5) {
      return;
    }

    await sync();
  }

  /// Public method for initial sync after authentication
  Future<bool> syncAll() async {
    return await sync();
  }

  /// Helper methods to work with different model types
  String _getLocalUuid(dynamic record) {
    if (record is Transaction) {
      return record.localUuid;
    }
    if (record is Category) {
      return record.localUuid;
    }
    if (record is Budget) {
      return record.localUuid;
    }
    if (record is Loan) {
      return record.localUuid;
    }
    throw Exception('Unknown record type');
  }

  DateTime _getLastModified(dynamic record) {
    if (record is Transaction) {
      return record.lastModified;
    }
    if (record is Category) {
      return record.lastModified;
    }
    if (record is Budget) {
      return record.lastModified;
    }
    if (record is Loan) {
      return record.lastModified;
    }
    throw Exception('Unknown record type');
  }

  bool _getNeedsSync(dynamic record) {
    if (record is Transaction) {
      return record.needsSync;
    }
    if (record is Category) {
      return record.needsSync;
    }
    if (record is Budget) {
      return record.needsSync;
    }
    if (record is Loan) {
      return record.needsSync;
    }
    throw Exception('Unknown record type');
  }

  void _setNeedsSync(dynamic record, bool value) {
    if (record is Transaction) {
      record.needsSync = value;
    } else if (record is Category) {
      record.needsSync = value;
    } else if (record is Budget) {
      record.needsSync = value;
    } else if (record is Loan) {
      record.needsSync = value;
    } else {
      throw Exception('Unknown record type');
    }
  }

  /// Convert record to JSON with proper foreign key handling
  Future<Map<String, dynamic>?> _toJsonWithForeignKeys(dynamic record) async {
    if (record is Category) {
      return record.toJson();
    }
    if (record is Loan) {
      return record.toJson();
    }
    if (record is Transaction) {
      // Find supabaseId for categoryUuid
      final category = _categoryBox.values.firstWhere(
        (c) => c.localUuid == record.categoryUuid,
        orElse: () => throw Exception('Category not found for transaction'),
      );

      if (category.supabaseId == null) {
        // Category hasn't been synced yet, skip this transaction for now
        return null;
      }

      int? loanSupabaseId;
      if (record.loanUuid != null) {
        final loan = _loanBox.values.firstWhere(
          (l) => l.localUuid == record.loanUuid,
          orElse: () => throw Exception('Loan not found for transaction'),
        );

        if (loan.supabaseId == null) {
          // Loan hasn't been synced yet, skip this transaction for now
          return null;
        }
        loanSupabaseId = loan.supabaseId;
      }

      return record.toJson(
        categorySupabaseId: category.supabaseId!,
        loanSupabaseId: loanSupabaseId,
      );
    }
    if (record is Budget) {
      // Find supabaseId for categoryUuid
      final category = _categoryBox.values.firstWhere(
        (c) => c.localUuid == record.categoryUuid,
        orElse: () => throw Exception('Category not found for budget'),
      );

      if (category.supabaseId == null) {
        // Category hasn't been synced yet, skip this budget for now
        return null;
      }

      return record.toJson(categorySupabaseId: category.supabaseId!);
    }
    throw Exception('Unknown record type');
  }

  /// Create Transaction from JSON with local UUID mapping
  Transaction _transactionFromJson(Map<String, dynamic> json) {
    // Find local category UUID from supabaseId
    final categorySupabaseId = json['category_id'] as int?;
    String? categoryUuid;
    if (categorySupabaseId != null) {
      try {
        final category = _categoryBox.values.firstWhere(
          (c) => c.supabaseId == categorySupabaseId,
        );
        categoryUuid = category.localUuid;
      } catch (e) {
        // Category not found locally, this shouldn't happen in normal sync
        debugPrint('Category with supabaseId $categorySupabaseId not found locally');
        throw Exception('Category not found for transaction');
      }
    }

    // Find local loan UUID from supabaseId
    final loanSupabaseId = json['loan_id'] as int?;
    String? loanUuid;
    if (loanSupabaseId != null) {
      try {
        final loan = _loanBox.values.firstWhere(
          (l) => l.supabaseId == loanSupabaseId,
        );
        loanUuid = loan.localUuid;
      } catch (e) {
        // Loan not found locally
        debugPrint('Loan with supabaseId $loanSupabaseId not found locally');
        throw Exception('Loan not found for transaction');
      }
    }

    return Transaction(
      localUuid: json['local_uuid'],
      date: DateTime.parse(json['date']),
      description: json['description'],
      amount: (json['amount'] as num).toDouble(),
      type: json['type'],
      categoryUuid: categoryUuid ?? '',
      loanUuid: loanUuid,
      userId: json['user_id'],
      supabaseId: json['id'],
      lastModified: DateTime.parse(json['last_modified']),
      isDeleted: json['is_deleted'] ?? false,
      needsSync: false,
    );
  }

  /// Create Budget from JSON with local UUID mapping
  Budget _budgetFromJson(Map<String, dynamic> json) {
    // Find local category UUID from supabaseId
    final categorySupabaseId = json['category_id'] as int?;
    String? categoryUuid;
    if (categorySupabaseId != null) {
      try {
        final category = _categoryBox.values.firstWhere(
          (c) => c.supabaseId == categorySupabaseId,
        );
        categoryUuid = category.localUuid;
      } catch (e) {
        // Category not found locally
        debugPrint('Category with supabaseId $categorySupabaseId not found locally');
        throw Exception('Category not found for budget');
      }
    }

    return Budget(
      localUuid: json['local_uuid'],
      period: DateTime.parse(json['period']),
      amount: (json['amount'] as num).toDouble(),
      categoryUuid: categoryUuid ?? '',
      userId: json['user_id'],
      supabaseId: json['id'],
      lastModified: DateTime.parse(json['last_modified']),
      isDeleted: json['is_deleted'] ?? false,
      needsSync: false,
    );
  }
}
